#!/usr/bin/env python3
"""
Test script to verify DS Task AI News setup is working correctly.
"""

def test_imports():
    """Test that all required packages can be imported."""
    print("🧪 Testing imports...")
    
    try:
        import fastapi
        print("✅ FastAPI imported successfully")
    except ImportError as e:
        print(f"❌ FastAPI import failed: {e}")
        return False
    
    try:
        import uvicorn
        print("✅ Uvicorn imported successfully")
    except ImportError as e:
        print(f"❌ Uvicorn import failed: {e}")
        return False
    
    try:
        import pinecone
        print("✅ Pinecone imported successfully")
    except ImportError as e:
        print(f"❌ Pinecone import failed: {e}")
        return False
    
    try:
        import cohere
        print("✅ Cohere imported successfully")
    except ImportError as e:
        print(f"❌ Cohere import failed: {e}")
        return False
    
    try:
        import groq
        print("✅ Groq imported successfully")
    except ImportError as e:
        print(f"❌ Groq import failed: {e}")
        return False
    
    return True

def test_backend_modules():
    """Test that backend modules can be imported."""
    print("\n🧪 Testing backend modules...")
    
    try:
        from backend.config import get_settings
        print("✅ Config module imported successfully")
    except ImportError as e:
        print(f"❌ Config module import failed: {e}")
        return False
    
    try:
        from backend.main import app
        print("✅ Main FastAPI app imported successfully")
    except ImportError as e:
        print(f"❌ Main app import failed: {e}")
        return False
    
    return True

def test_configuration():
    """Test configuration loading."""
    print("\n🧪 Testing configuration...")
    
    try:
        from backend.config import get_settings
        settings = get_settings()
        print("✅ Settings loaded successfully")
        print(f"📊 RSS feed categories: {len(settings.rss_feeds)}")
        print(f"🔧 API host: {settings.api_host}:{settings.api_port}")
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 DS Task AI News - Setup Verification")
    print("=" * 50)
    
    all_passed = True
    
    # Test imports
    if not test_imports():
        all_passed = False
    
    # Test backend modules
    if not test_backend_modules():
        all_passed = False
    
    # Test configuration
    if not test_configuration():
        all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All tests passed! Your setup is ready.")
        print("\n📋 Next steps:")
        print("1. Set up your API keys in .env file")
        print("2. Run the backend: cd backend && python main.py")
        print("3. Run the frontend: cd frontend && ./start-frontend.sh")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    return all_passed

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
