Collecting pinecone
  Downloading pinecone-7.2.0-py3-none-any.whl.metadata (9.5 kB)
Requirement already satisfied: certifi>=2019.11.17 in /Users/<USER>/Desktop/ds_task_ai_news/venv/lib/python3.12/site-packages (from pinecone) (2025.6.15)
Collecting pinecone-plugin-assistant<2.0.0,>=1.6.0 (from pinecone)
  Using cached pinecone_plugin_assistant-1.7.0-py3-none-any.whl.metadata (28 kB)
Requirement already satisfied: pinecone-plugin-interface<0.0.8,>=0.0.7 in /Users/<USER>/Desktop/ds_task_ai_news/venv/lib/python3.12/site-packages (from pinecone) (0.0.7)
Requirement already satisfied: python-dateutil>=2.5.3 in /Users/<USER>/Desktop/ds_task_ai_news/venv/lib/python3.12/site-packages (from pinecone) (2.8.2)
Requirement already satisfied: typing-extensions>=3.7.4 in /Users/<USER>/Desktop/ds_task_ai_news/venv/lib/python3.12/site-packages (from pinecone) (4.14.0)
Requirement already satisfied: urllib3>=1.26.5 in /Users/<USER>/Desktop/ds_task_ai_news/venv/lib/python3.12/site-packages (from pinecone) (2.5.0)
Collecting packaging<25.0,>=24.2 (from pinecone-plugin-assistant<2.0.0,>=1.6.0->pinecone)
  Using cached packaging-24.2-py3-none-any.whl.metadata (3.2 kB)
Collecting requests<3.0.0,>=2.32.3 (from pinecone-plugin-assistant<2.0.0,>=1.6.0->pinecone)
  Using cached requests-2.32.4-py3-none-any.whl.metadata (4.9 kB)
Requirement already satisfied: charset_normalizer<4,>=2 in /Users/<USER>/Desktop/ds_task_ai_news/venv/lib/python3.12/site-packages (from requests<3.0.0,>=2.32.3->pinecone-plugin-assistant<2.0.0,>=1.6.0->pinecone) (3.4.2)
Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/Desktop/ds_task_ai_news/venv/lib/python3.12/site-packages (from requests<3.0.0,>=2.32.3->pinecone-plugin-assistant<2.0.0,>=1.6.0->pinecone) (3.10)
Requirement already satisfied: six>=1.5 in /Users/<USER>/Desktop/ds_task_ai_news/venv/lib/python3.12/site-packages (from python-dateutil>=2.5.3->pinecone) (1.17.0)
Downloading pinecone-7.2.0-py3-none-any.whl (524 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 524.3/524.3 kB 791.5 kB/s eta 0:00:00
Using cached pinecone_plugin_assistant-1.7.0-py3-none-any.whl (239 kB)
Using cached packaging-24.2-py3-none-any.whl (65 kB)
Using cached requests-2.32.4-py3-none-any.whl (64 kB)
Installing collected packages: requests, packaging, pinecone-plugin-assistant, pinecone
  Attempting uninstall: requests
    Found existing installation: requests 2.31.0
    Uninstalling requests-2.31.0:
      Successfully uninstalled requests-2.31.0
  Attempting uninstall: packaging
    Found existing installation: packaging 25.0
    Uninstalling packaging-25.0:
      Successfully uninstalled packaging-25.0

Successfully installed packaging-24.2 pinecone-7.2.0 pinecone-plugin-assistant-1.7.0 requests-2.32.4
