"""
FastAPI main application for DS Task AI News.
"""
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime
from loguru import logger

try:
    # Try relative imports first (when run as module)
    from .config import get_settings
    from .news_fetcher import get_news_fetcher
    from .vector_store import get_vector_store
    from .recommender import get_news_recommender
except ImportError:
    # Fall back to absolute imports (when run directly)
    from config import get_settings
    from news_fetcher import get_news_fetcher
    from vector_store import get_vector_store
    from recommender import get_news_recommender

# Initialize settings
settings = get_settings()

# Create FastAPI app
app = FastAPI(
    title="DS Task AI News",
    description="AI-powered news retrieval and recommendation system",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for API requests/responses
class SearchRequest(BaseModel):
    query: str
    max_results: Optional[int] = None
    category: Optional[str] = None

class RecommendationResponse(BaseModel):
    articles: List[Dict[str, Any]]
    query: str
    total_results: int
    ai_insight: Optional[str] = None



class StatsResponse(BaseModel):
    total_articles: int
    index_name: str
    environment: str
    last_fetch: Optional[str] = None


# Global variables for tracking
last_fetch_time = None


@app.on_event("startup")
async def startup_event():
    """Initialize the application on startup."""
    logger.info("Starting DS Task AI News application")

    # Initialize all components
    try:
        get_news_fetcher()
        get_vector_store()
        get_news_recommender()
        logger.info("All components initialized successfully")

    except Exception as e:
        logger.error(f"Error initializing components: {str(e)}")
        raise


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "DS Task AI News API",
        "version": "1.0.0",
        "description": "AI-powered news retrieval and recommendation system",
        "endpoints": {
            "search": "/search - Search for news articles (optionally by category)",
            "recommendations": "/recommendations - Get personalized recommendations",
            "fetch": "/fetch-news - Fetch latest news from RSS feeds",
            "fetch_category": "/fetch-news/{category} - Fetch news from specific category",
            "categories": "/categories - Get available news categories",
            "stats": "/stats - Get database statistics",
            "trending": "/trending - Get trending topics"
        }
    }


@app.post("/search", response_model=RecommendationResponse)
async def search_articles(request: SearchRequest):
    """
    Search for news articles based on a query, optionally filtered by category.
    """
    try:
        if request.category:
            logger.info(f"Search request: {request.query} in category: {request.category}")
        else:
            logger.info(f"Search request: {request.query}")

        vector_store = get_vector_store()

        if request.category:
            # Search within specific category
            articles = vector_store.search_by_category(
                query=request.query,
                category=request.category,
                n_results=request.max_results
            )
        else:
            # Search across all categories
            recommender = get_news_recommender()
            articles = recommender.get_recommendations(
                query=request.query,
                max_results=request.max_results
            )

        # Extract AI insight if available
        ai_insight = None
        if articles and 'ai_insight' in articles[0]:
            ai_insight = articles[0]['ai_insight']
            # Remove from individual article to avoid duplication
            del articles[0]['ai_insight']

        return RecommendationResponse(
            articles=articles,
            query=request.query,
            total_results=len(articles),
            ai_insight=ai_insight
        )

    except Exception as e:
        logger.error(f"Error in search: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/recommendations", response_model=RecommendationResponse)
async def get_recommendations(request: SearchRequest):
    """
    Get personalized news recommendations.
    This is an alias for the search endpoint with the same functionality.
    """
    return await search_articles(request)


@app.get("/fetch-news")
async def fetch_news(save_to_db: bool = True, background_tasks: BackgroundTasks = BackgroundTasks()):
    """
    Fetch latest news from RSS feeds.
    """
    try:
        logger.info("Manual news fetch requested")

        if save_to_db:
            # Run in background to avoid timeout
            background_tasks.add_task(fetch_and_store_news)
            return {
                "message": "News fetching started in background",
                "status": "processing"
            }
        else:
            # Fetch without storing
            news_fetcher = get_news_fetcher()
            articles = news_fetcher.fetch_all_feeds()

            return {
                "message": "News fetched successfully",
                "articles_count": len(articles),
                "status": "completed"
            }

    except Exception as e:
        logger.error(f"Error fetching news: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


async def fetch_and_store_news():
    """Background task to fetch and store news articles."""
    global last_fetch_time

    try:
        logger.info("Starting background news fetch and store")

        # Fetch articles
        news_fetcher = get_news_fetcher()
        articles = news_fetcher.fetch_all_feeds()

        if articles:
            # Save raw articles
            news_fetcher.save_raw_articles(articles)

            # Add to vector store
            vector_store = get_vector_store()
            added_count = vector_store.add_articles(articles)

            last_fetch_time = datetime.now().isoformat()
            logger.info(f"Successfully processed {added_count} articles")
        else:
            logger.warning("No articles fetched")

    except Exception as e:
        logger.error(f"Error in background fetch and store: {str(e)}")


async def fetch_and_store_news_by_category(category: str):
    """Background task to fetch and store news articles from a specific category."""
    global last_fetch_time

    try:
        logger.info(f"Starting background news fetch and store for category: {category}")

        # Fetch articles from specific category
        news_fetcher = get_news_fetcher()
        articles = news_fetcher.fetch_by_category(category)

        if articles:
            # Save raw articles
            news_fetcher.save_raw_articles(articles)

            # Add to vector store
            vector_store = get_vector_store()
            added_count = vector_store.add_articles(articles)

            last_fetch_time = datetime.now().isoformat()
            logger.info(f"Successfully processed {added_count} articles from {category} category")
        else:
            logger.warning(f"No articles fetched from {category} category")

    except Exception as e:
        logger.error(f"Error in background fetch and store for category {category}: {str(e)}")


@app.get("/stats", response_model=StatsResponse)
async def get_stats():
    """
    Get database and application statistics.
    """
    try:
        vector_store = get_vector_store()
        stats = vector_store.get_collection_stats()
        
        return StatsResponse(
            total_articles=stats.get('total_articles', 0),
            index_name=stats.get('index_name', ''),
            environment=stats.get('environment', ''),
            last_fetch=last_fetch_time
        )
        
    except Exception as e:
        logger.error(f"Error getting stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/trending")
async def get_trending_topics(limit: int = 10):
    """
    Get trending topics from recent news.
    """
    try:
        logger.info(f"Getting trending topics (limit: {limit})")

        recommender = get_news_recommender()
        trending_topics = recommender.get_trending_topics(limit=limit)

        return {
            "trending_topics": trending_topics,
            "count": len(trending_topics),
            "generated_at": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting trending topics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/categories")
async def get_categories():
    """
    Get available news categories.
    """
    try:
        news_fetcher = get_news_fetcher()
        categories = news_fetcher.get_available_categories()

        return {
            "categories": categories,
            "count": len(categories)
        }

    except Exception as e:
        logger.error(f"Error getting categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/fetch-news/{category}")
async def fetch_news_by_category(category: str, background_tasks: BackgroundTasks):
    """
    Fetch latest news from RSS feeds of a specific category.
    """
    try:
        logger.info(f"Manual news fetch requested for category: {category}")

        # Run in background to avoid timeout
        background_tasks.add_task(fetch_and_store_news_by_category, category)
        return {
            "message": f"News fetching for {category} category started in background",
            "category": category,
            "status": "processing"
        }

    except Exception as e:
        logger.error(f"Error fetching news for category {category}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/article/{article_id}")
async def get_article(article_id: str):
    """
    Get a specific article by ID.
    """
    try:
        vector_store = get_vector_store()
        article = vector_store.get_article_by_id(article_id)
        
        if not article:
            raise HTTPException(status_code=404, detail="Article not found")
        
        return article
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting article {article_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/clear-database")
async def clear_database():
    """
    Clear all articles from the database.
    Use with caution!
    """
    try:
        logger.warning("Database clear requested")
        
        vector_store = get_vector_store()
        success = vector_store.clear_collection()
        
        if success:
            global last_fetch_time
            last_fetch_time = None
            return {"message": "Database cleared successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to clear database")
        
    except Exception as e:
        logger.error(f"Error clearing database: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    import uvicorn
    
    logger.info(f"Starting server on {settings.api_host}:{settings.api_port}")
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.api_reload
    )
