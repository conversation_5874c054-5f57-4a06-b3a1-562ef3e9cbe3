# DS Task AI News - Environment Variables Template
# Copy this file to .env and fill in your actual API keys

# API Keys (Required)
COHERE_API_KEY=your_cohere_api_key_here
GROQ_API_KEY=your_groq_api_key_here
PINECONE_API_KEY=your_pinecone_api_key_here

# Pinecone Configuration (Updated for new API)
PINECONE_INDEX_NAME=news-articles
PINECONE_NAMESPACE=default

# Data Directories
RAW_NEWS_DIR=./data/raw_news
PROCESSED_NEWS_DIR=./data/processed_news

# Embedding Settings
EMBEDDING_MODEL=embed-english-v3.0
EMBEDDING_DIMENSION=1024

# LLM Settings
GROQ_MODEL=mixtral-8x7b-32768
MAX_TOKENS=1000
TEMPERATURE=0.7

# News Fetching Settings
FETCH_INTERVAL_HOURS=6
MAX_ARTICLES_PER_FEED=50

# API Settings
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=true

# Recommendation Settings
MAX_RECOMMENDATIONS=10
SIMILARITY_THRESHOLD=0.7
