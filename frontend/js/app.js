// DS Task AI News Frontend Application
class NewsApp {
  constructor() {
    this.apiBaseUrl = "http://localhost:8000";
    this.currentSection = "search";
    this.categories = [];

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.loadCategories();
    this.showSection("search");
  }

  setupEventListeners() {
    // Navigation
    document.querySelectorAll(".nav-btn").forEach((btn) => {
      btn.addEventListener("click", (e) => {
        const section = e.target.closest(".nav-btn").dataset.section;
        this.showSection(section);
      });
    });

    // Search functionality
    document
      .getElementById("search-btn")
      .addEventListener("click", () => this.performSearch());
    document
      .getElementById("search-input")
      .addEventListener("keypress", (e) => {
        if (e.key === "Enter") this.performSearch();
      });

    // Category functionality
    document
      .getElementById("fetch-all-btn")
      .addEventListener("click", () => this.fetchAllNews());
    document
      .getElementById("get-trending-btn")
      .addEventListener("click", () => this.getTrendingTopics());
    document
      .getElementById("refresh-stats-btn")
      .addEventListener("click", () => this.getStats());
  }

  showSection(sectionName) {
    // Update navigation
    document.querySelectorAll(".nav-btn").forEach((btn) => {
      btn.classList.remove("active");
    });
    document
      .querySelector(`[data-section="${sectionName}"]`)
      .classList.add("active");

    // Update sections
    document.querySelectorAll(".section").forEach((section) => {
      section.classList.remove("active");
    });
    document.getElementById(`${sectionName}-section`).classList.add("active");

    this.currentSection = sectionName;

    // Load section-specific data
    if (sectionName === "categories") {
      this.loadCategories();
    } else if (sectionName === "stats") {
      this.getStats();
    }
  }

  async makeApiCall(endpoint, options = {}) {
    this.showLoading(true);
    try {
      const response = await fetch(`${this.apiBaseUrl}${endpoint}`, {
        headers: {
          "Content-Type": "application/json",
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("API call failed:", error);
      this.showToast(`Error: ${error.message}`, "error");
      throw error;
    } finally {
      this.showLoading(false);
    }
  }

  async loadCategories() {
    try {
      const data = await this.makeApiCall("/categories");
      this.categories = data.categories;
      this.updateCategorySelect();
      this.updateCategoriesGrid();
      this.updateCategoryFetchButtons();
    } catch (error) {
      console.error("Failed to load categories:", error);
    }
  }

  updateCategorySelect() {
    const select = document.getElementById("category-filter");
    select.innerHTML = '<option value="">All Categories</option>';

    this.categories.forEach((category) => {
      const option = document.createElement("option");
      option.value = category;
      option.textContent = this.formatCategoryName(category);
      select.appendChild(option);
    });
  }

  updateCategoriesGrid() {
    const grid = document.getElementById("categories-grid");
    grid.innerHTML = "";

    const categoryIcons = {
      mainstream_news: "fas fa-newspaper",
      music: "fas fa-music",
      gaming: "fas fa-gamepad",
      tech: "fas fa-laptop-code",
      lifestyle: "fas fa-heart",
    };

    this.categories.forEach((category) => {
      const card = document.createElement("div");
      card.className = "category-card";
      card.innerHTML = `
                <i class="${categoryIcons[category] || "fas fa-folder"}"></i>
                <h3>${this.formatCategoryName(category)}</h3>
                <p>Click to search in this category</p>
            `;
      card.addEventListener("click", () => this.searchByCategory(category));
      grid.appendChild(card);
    });
  }

  updateCategoryFetchButtons() {
    const container = document.getElementById("category-fetch-buttons");
    container.innerHTML = "";

    this.categories.forEach((category) => {
      const button = document.createElement("button");
      button.className = "category-fetch-btn";
      button.textContent = `Fetch ${this.formatCategoryName(category)}`;
      button.addEventListener("click", () =>
        this.fetchNewsByCategory(category)
      );
      container.appendChild(button);
    });
  }

  async performSearch() {
    const query = document.getElementById("search-input").value.trim();
    const category = document.getElementById("category-filter").value;
    const maxResults = document.getElementById("max-results").value || 10;

    if (!query) {
      this.showToast("Please enter a search query", "warning");
      return;
    }

    try {
      const requestBody = {
        query: query,
        max_results: parseInt(maxResults),
      };

      if (category) {
        requestBody.category = category;
      }

      const data = await this.makeApiCall("/search", {
        method: "POST",
        body: JSON.stringify(requestBody),
      });

      this.displaySearchResults(data);
      this.showToast(`Found ${data.total_results} articles`, "success");
    } catch (error) {
      console.error("Search failed:", error);
    }
  }

  searchByCategory(category) {
    document.getElementById("category-filter").value = category;
    this.showSection("search");

    // Auto-search if there's a query
    const query = document.getElementById("search-input").value.trim();
    if (query) {
      this.performSearch();
    }
  }

  displaySearchResults(data) {
    const container = document.getElementById("search-results");

    if (!data.articles || data.articles.length === 0) {
      container.innerHTML = `
                <div class="placeholder-message">
                    <i class="fas fa-search fa-3x"></i>
                    <h3>No Results Found</h3>
                    <p>Try adjusting your search query or category filter.</p>
                </div>
            `;
      return;
    }

    let html = "";

    if (data.ai_insight) {
      html += `
                <div class="ai-insight" style="background: #e3f2fd; border-left: 4px solid #2196f3; padding: 1rem; margin-bottom: 1rem; border-radius: 5px;">
                    <h3 style="color: #1976d2; margin-bottom: 0.5rem;"><i class="fas fa-brain"></i> AI Insight</h3>
                    <p style="color: #333; margin: 0;">${data.ai_insight}</p>
                </div>
            `;
    }

    data.articles.forEach((article) => {
      html += `
                <div class="article-card">
                    <div class="article-title">
                        <a href="${article.link}" target="_blank">${
        article.title
      }</a>
                    </div>
                    <div class="article-meta">
                        <span class="article-category">${this.formatCategoryName(
                          article.category
                        )}</span>
                        <span class="article-source">${
                          article.source_name
                        }</span>
                        <span class="article-date">${this.formatDate(
                          article.published
                        )}</span>
                        ${
                          article.similarity_score
                            ? `<span class="similarity-score">${Math.round(
                                article.similarity_score * 100
                              )}% match</span>`
                            : ""
                        }
                    </div>
                    <div class="article-summary">${article.summary}</div>
                </div>
            `;
    });

    container.innerHTML = html;
  }

  async fetchAllNews() {
    try {
      await this.makeApiCall("/fetch-news", {
        method: "POST",
        body: JSON.stringify({ save_to_db: true }),
      });
      this.showToast("News fetching started in background", "success");
    } catch (error) {
      console.error("Failed to fetch news:", error);
    }
  }

  async fetchNewsByCategory(category) {
    try {
      await this.makeApiCall(`/fetch-news/${category}`, {
        method: "POST",
      });
      this.showToast(
        `Fetching ${this.formatCategoryName(category)} news started`,
        "success"
      );
    } catch (error) {
      console.error("Failed to fetch category news:", error);
    }
  }

  async getTrendingTopics() {
    const limit = document.getElementById("trending-limit").value || 10;

    try {
      const data = await this.makeApiCall(`/trending?limit=${limit}`);
      this.displayTrendingTopics(data);
    } catch (error) {
      console.error("Failed to get trending topics:", error);
    }
  }

  displayTrendingTopics(data) {
    const container = document.getElementById("trending-results");

    if (!data.trending_topics || data.trending_topics.length === 0) {
      container.innerHTML = `
                <div class="placeholder-message">
                    <i class="fas fa-fire fa-3x"></i>
                    <h3>No Trending Topics</h3>
                    <p>No trending topics available at the moment.</p>
                </div>
            `;
      return;
    }

    let html = "";
    data.trending_topics.forEach((topic) => {
      html += `
                <div class="trending-topic">
                    <h4>${topic.topic}</h4>
                    <p>${topic.description}</p>
                    <div class="trending-reason">${topic.reason}</div>
                </div>
            `;
    });

    container.innerHTML = html;
    this.showToast(`Found ${data.count} trending topics`, "success");
  }

  async getStats() {
    try {
      const data = await this.makeApiCall("/stats");
      this.displayStats(data);
    } catch (error) {
      console.error("Failed to get stats:", error);
    }
  }

  displayStats(data) {
    const container = document.getElementById("stats-display");

    const html = `
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">${data.total_articles.toLocaleString()}</div>
                    <div class="stat-label">Total Articles</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${data.index_name}</div>
                    <div class="stat-label">Pinecone Index</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${data.environment}</div>
                    <div class="stat-label">Environment</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${
                      data.last_fetch
                        ? this.formatDate(data.last_fetch)
                        : "Never"
                    }</div>
                    <div class="stat-label">Last Fetch</div>
                </div>
            </div>
        `;

    container.innerHTML = html;
    this.showToast("Stats refreshed", "success");
  }

  formatCategoryName(category) {
    return category.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
  }

  formatDate(dateString) {
    try {
      const date = new Date(dateString);
      return (
        date.toLocaleDateString() +
        " " +
        date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
      );
    } catch {
      return dateString;
    }
  }

  showLoading(show) {
    const overlay = document.getElementById("loading-overlay");
    if (show) {
      overlay.classList.add("show");
    } else {
      overlay.classList.remove("show");
    }
  }

  showToast(message, type = "info") {
    const container = document.getElementById("toast-container");
    const toast = document.createElement("div");
    toast.className = `toast ${type}`;
    toast.textContent = message;

    container.appendChild(toast);

    // Trigger animation
    setTimeout(() => toast.classList.add("show"), 100);

    // Remove after 3 seconds
    setTimeout(() => {
      toast.classList.remove("show");
      setTimeout(() => container.removeChild(toast), 300);
    }, 3000);
  }
}

// Initialize the app when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  new NewsApp();
});
