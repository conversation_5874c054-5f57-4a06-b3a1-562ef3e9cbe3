#!/bin/bash

# DS Task AI News - Frontend Start Script

echo "🌐 Starting DS Task AI News Frontend..."

# Check if Python is available
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo "❌ Python is not installed or not in PATH"
    echo "Please install Python to run the frontend server"
    exit 1
fi

# Get the directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Change to frontend directory
cd "$SCRIPT_DIR"

# Start the server
echo "🚀 Starting frontend server..."
echo "📍 Frontend will be available at: http://localhost:3000"
echo "🔗 Make sure the backend is running at: http://localhost:8000"
echo ""

$PYTHON_CMD serve.py
