<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DS Task AI News</title>
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <h1 class="logo">
                    <i class="fas fa-newspaper"></i>
                    DS Task AI News
                </h1>
                <nav class="nav">
                    <button class="nav-btn active" data-section="search">
                        <i class="fas fa-search"></i> Search
                    </button>
                    <button class="nav-btn" data-section="categories">
                        <i class="fas fa-tags"></i> Categories
                    </button>
                    <button class="nav-btn" data-section="trending">
                        <i class="fas fa-fire"></i> Trending
                    </button>
                    <button class="nav-btn" data-section="stats">
                        <i class="fas fa-chart-bar"></i> Stats
                    </button>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Search Section -->
            <section id="search-section" class="section active">
                <div class="search-container">
                    <h2>Search News Articles</h2>
                    <div class="search-form">
                        <div class="search-input-group">
                            <input type="text" id="search-input" placeholder="Enter your search query..." class="search-input">
                            <button id="search-btn" class="search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <div class="search-filters">
                            <select id="category-filter" class="category-select">
                                <option value="">All Categories</option>
                            </select>
                            <input type="number" id="max-results" placeholder="Max results" min="1" max="50" value="10" class="max-results-input">
                        </div>
                    </div>
                </div>
                
                <!-- Search Results -->
                <div id="search-results" class="results-container">
                    <div class="welcome-message">
                        <i class="fas fa-search fa-3x"></i>
                        <h3>Welcome to DS Task AI News</h3>
                        <p>Search for news articles using AI-powered recommendations. Enter a query above to get started.</p>
                    </div>
                </div>
            </section>

            <!-- Categories Section -->
            <section id="categories-section" class="section">
                <div class="categories-container">
                    <h2>News Categories</h2>
                    <div id="categories-grid" class="categories-grid">
                        <!-- Categories will be loaded here -->
                    </div>
                    
                    <div class="fetch-controls">
                        <h3>Fetch Latest News</h3>
                        <div class="fetch-buttons">
                            <button id="fetch-all-btn" class="fetch-btn primary">
                                <i class="fas fa-download"></i> Fetch All Categories
                            </button>
                        </div>
                        <div id="category-fetch-buttons" class="category-fetch-buttons">
                            <!-- Category-specific fetch buttons will be added here -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- Trending Section -->
            <section id="trending-section" class="section">
                <div class="trending-container">
                    <h2>Trending Topics</h2>
                    <div class="trending-controls">
                        <input type="number" id="trending-limit" placeholder="Number of topics" min="1" max="20" value="10" class="trending-limit-input">
                        <button id="get-trending-btn" class="trending-btn">
                            <i class="fas fa-fire"></i> Get Trending Topics
                        </button>
                    </div>
                    <div id="trending-results" class="trending-results">
                        <div class="placeholder-message">
                            <i class="fas fa-fire fa-3x"></i>
                            <h3>Discover Trending Topics</h3>
                            <p>Click the button above to see what's trending in the news right now.</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Stats Section -->
            <section id="stats-section" class="section">
                <div class="stats-container">
                    <h2>Database Statistics</h2>
                    <button id="refresh-stats-btn" class="stats-btn">
                        <i class="fas fa-sync-alt"></i> Refresh Stats
                    </button>
                    <div id="stats-display" class="stats-display">
                        <div class="placeholder-message">
                            <i class="fas fa-chart-bar fa-3x"></i>
                            <h3>Database Statistics</h3>
                            <p>Click refresh to see current database statistics.</p>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin fa-3x"></i>
            <p>Loading...</p>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 DS Task AI News. Powered by AI and Pinecone.</p>
        </div>
    </footer>

    <script src="js/app.js"></script>
</body>
</html>
