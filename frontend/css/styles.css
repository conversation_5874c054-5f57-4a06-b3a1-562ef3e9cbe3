/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    font-size: 1.8rem;
    font-weight: bold;
    color: #667eea;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav {
    display: flex;
    gap: 1rem;
}

.nav-btn {
    background: none;
    border: 2px solid transparent;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-btn:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.nav-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* Main Content */
.main {
    padding: 2rem 0;
    min-height: calc(100vh - 200px);
}

.section {
    display: none;
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.section.active {
    display: block;
}

/* Search Section */
.search-container h2 {
    margin-bottom: 1.5rem;
    color: #333;
    text-align: center;
}

.search-form {
    margin-bottom: 2rem;
}

.search-input-group {
    display: flex;
    margin-bottom: 1rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.search-input {
    flex: 1;
    padding: 1rem;
    border: 2px solid #e1e5e9;
    border-radius: 25px 0 0 25px;
    font-size: 1rem;
    outline: none;
    transition: border-color 0.3s ease;
}

.search-input:focus {
    border-color: #667eea;
}

.search-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 1rem 1.5rem;
    border-radius: 0 25px 25px 0;
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-btn:hover {
    background: #5a6fd8;
}

.search-filters {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.category-select,
.max-results-input {
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 25px;
    outline: none;
    transition: border-color 0.3s ease;
}

.category-select:focus,
.max-results-input:focus {
    border-color: #667eea;
}

/* Results Container */
.results-container {
    margin-top: 2rem;
}

.welcome-message,
.placeholder-message {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.welcome-message i,
.placeholder-message i {
    color: #667eea;
    margin-bottom: 1rem;
}

/* Article Cards */
.article-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-left: 4px solid #667eea;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.article-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.article-title {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: #333;
}

.article-title a {
    text-decoration: none;
    color: inherit;
}

.article-title a:hover {
    color: #667eea;
}

.article-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #666;
    flex-wrap: wrap;
}

.article-category {
    background: #667eea;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.article-summary {
    color: #555;
    line-height: 1.6;
}

.similarity-score {
    background: #28a745;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
    font-size: 0.8rem;
}

/* Categories Section */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.category-card {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.category-card:hover {
    transform: translateY(-5px);
}

.category-card i {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.category-card h3 {
    margin-bottom: 0.5rem;
    text-transform: capitalize;
}

.fetch-controls {
    border-top: 1px solid #e1e5e9;
    padding-top: 2rem;
}

.fetch-controls h3 {
    margin-bottom: 1rem;
    text-align: center;
}

.fetch-buttons {
    display: flex;
    justify-content: center;
    margin-bottom: 1rem;
}

.fetch-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.fetch-btn:hover {
    background: #218838;
}

.category-fetch-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.category-fetch-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background 0.3s ease;
}

.category-fetch-btn:hover {
    background: #5a6fd8;
}

/* Trending Section */
.trending-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.trending-limit-input {
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 25px;
    outline: none;
    width: 200px;
}

.trending-btn {
    background: #ff6b35;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.trending-btn:hover {
    background: #e55a2b;
}

.trending-topic {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-left: 4px solid #ff6b35;
}

.trending-topic h4 {
    color: #333;
    margin-bottom: 0.5rem;
}

.trending-topic p {
    color: #666;
    margin-bottom: 0.5rem;
}

.trending-reason {
    font-style: italic;
    color: #888;
    font-size: 0.9rem;
}

/* Stats Section */
.stats-container {
    text-align: center;
}

.stats-btn {
    background: #17a2b8;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    margin-bottom: 2rem;
    transition: background 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.stats-btn:hover {
    background: #138496;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
}

.loading-spinner i {
    color: #667eea;
    margin-bottom: 1rem;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
}

.toast {
    background: #333;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    margin-bottom: 0.5rem;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast.success {
    background: #28a745;
}

.toast.error {
    background: #dc3545;
}

.toast.warning {
    background: #ffc107;
    color: #333;
}

/* Footer */
.footer {
    background: rgba(255, 255, 255, 0.95);
    text-align: center;
    padding: 1rem 0;
    color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .search-input-group {
        flex-direction: column;
    }
    
    .search-input {
        border-radius: 25px;
        margin-bottom: 0.5rem;
    }
    
    .search-btn {
        border-radius: 25px;
    }
    
    .search-filters {
        flex-direction: column;
        align-items: center;
    }
    
    .categories-grid {
        grid-template-columns: 1fr;
    }
    
    .article-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
}
