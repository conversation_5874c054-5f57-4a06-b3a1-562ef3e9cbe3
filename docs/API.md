# DS Task AI News - API Documentation

## Overview

The DS Task AI News API provides endpoints for fetching, storing, and retrieving news articles using AI-powered recommendations and vector similarity search.

## Base URL

```
http://localhost:8000
```

## Authentication

Currently, the API does not require authentication. However, you need to configure API keys for Cohere and Groq services in your environment variables.

## Endpoints

### 1. Root Endpoint

**GET** `/`

Returns basic API information and available endpoints.

**Response:**

```json
{
  "message": "DS Task AI News API",
  "version": "1.0.0",
  "description": "AI-powered news retrieval and recommendation system",
  "endpoints": {
    "search": "/search - Search for news articles",
    "recommendations": "/recommendations - Get personalized recommendations",
    "fetch": "/fetch-news - Fetch latest news from RSS feeds",
    "stats": "/stats - Get database statistics",
    "trending": "/trending - Get trending topics"
  }
}
```

### 2. Search Articles

**POST** `/search`

Search for news articles based on a query using vector similarity.

**Request Body:**

```json
{
  "query": "artificial intelligence technology",
  "max_results": 10,
  "category": "tech"
}
```

**Parameters:**

- `query` (required): Search query string
- `max_results` (optional): Maximum number of results to return
- `category` (optional): Filter results by category (mainstream_news, music, gaming, tech, lifestyle)

**Response:**

```json
{
  "articles": [
    {
      "id": "article_id",
      "title": "Article Title",
      "link": "https://example.com/article",
      "summary": "Article summary...",
      "published": "2024-01-01T12:00:00",
      "source_name": "Source Name",
      "similarity_score": 0.85,
      "content_preview": "Article content preview..."
    }
  ],
  "query": "artificial intelligence technology",
  "total_results": 5,
  "ai_insight": "These articles focus on recent AI developments..."
}
```

### 3. Get Recommendations

**POST** `/recommendations`

Get personalized news recommendations (alias for search endpoint).

**Request/Response:** Same as `/search`

### 4. Fetch News

**POST** `/fetch-news`

Manually trigger fetching of latest news from RSS feeds.

**Request Body:**

```json
{
  "save_to_db": true
}
```

**Response:**

```json
{
  "message": "News fetching started in background",
  "status": "processing"
}
```

### 5. Get Statistics

**GET** `/stats`

Get database and application statistics.

**Response:**

```json
{
  "total_articles": 1250,
  "index_name": "news-articles",
  "environment": "us-west1-gcp-free",
  "last_fetch": "2024-01-01T12:00:00"
}
```

### 6. Get Trending Topics

**GET** `/trending?limit=10`

Get trending topics from recent news articles.

**Query Parameters:**

- `limit` (optional): Number of trending topics to return (default: 10)

**Response:**

```json
{
  "trending_topics": [
    {
      "topic": "Artificial Intelligence",
      "description": "Recent developments in AI technology",
      "reason": "Multiple articles discussing AI breakthroughs"
    }
  ],
  "count": 5,
  "generated_at": "2024-01-01T12:00:00"
}
```

### 7. Get Article by ID

**GET** `/article/{article_id}`

Get a specific article by its ID.

**Response:**

```json
{
  "id": "article_id",
  "title": "Article Title",
  "link": "https://example.com/article",
  "summary": "Article summary",
  "content": "Full article content",
  "published": "2024-01-01T12:00:00",
  "source_name": "Source Name",
  "source_url": "https://source.com/rss"
}
```

### 8. Get Categories

**GET** `/categories`

Get available news categories.

**Response:**

```json
{
  "categories": ["mainstream_news", "music", "gaming", "tech", "lifestyle"],
  "count": 5
}
```

### 9. Fetch News by Category

**POST** `/fetch-news/{category}`

Manually trigger fetching of latest news from a specific category.

**Path Parameters:**

- `category`: Category name (mainstream_news, music, gaming, tech, lifestyle)

**Response:**

```json
{
  "message": "News fetching for tech category started in background",
  "category": "tech",
  "status": "processing"
}
```

### 10. Clear Database

**DELETE** `/clear-database`

Clear all articles from the database. Use with caution!

**Response:**

```json
{
  "message": "Database cleared successfully"
}
```

## Error Responses

All endpoints return appropriate HTTP status codes and error messages:

```json
{
  "detail": "Error description"
}
```

Common status codes:

- `200`: Success
- `404`: Resource not found
- `422`: Validation error
- `500`: Internal server error

## Rate Limiting

Currently, no rate limiting is implemented. Consider implementing rate limiting for production use.

## CORS

CORS is enabled for all origins in development. Configure appropriately for production.
