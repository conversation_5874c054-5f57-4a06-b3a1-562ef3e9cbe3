# DS Task AI News - Setup Guide

## Prerequisites

- Python 3.8 or higher
- pip (Python package manager)
- API keys for:
  - Cohere (for embeddings and re-ranking)
  - Groq (for LLM analysis)
  - Pinecone (for vector database)

## Installation

### 1. Clone the Repository

```bash
git clone <repository-url>
cd ds_task_ai_news
```

### 2. Create Virtual Environment

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 3. Install Dependencies

```bash
cd backend
pip install -r requirements.txt
```

### 4. Configure Environment Variables

Copy the example environment file and configure your API keys:

```bash
cp .env.example .env
```

Edit `.env` file and add your API keys:

```env
COHERE_API_KEY=your_cohere_api_key_here
GROQ_API_KEY=your_groq_api_key_here
PINECONE_API_KEY=your_pinecone_api_key_here
```

## Getting API Keys

### Cohere API Key

1. Visit [Cohere Dashboard](https://dashboard.cohere.ai/)
2. Sign up or log in
3. Navigate to API Keys section
4. Create a new API key
5. Copy the key to your `.env` file

### Groq API Key

1. Visit [Groq Console](https://console.groq.com/)
2. Sign up or log in
3. Navigate to API Keys section
4. Create a new API key
5. Copy the key to your `.env` file

### Pinecone API Key

1. Visit [Pinecone Console](https://app.pinecone.io/)
2. Sign up or log in
3. Create a new project or use existing one
4. Navigate to API Keys section
5. Copy your API key to your `.env` file
6. Note: The new Pinecone API uses serverless indexes by default, no environment configuration needed

## Running the Application

### 1. Start the Backend Server

```bash
cd backend
python main.py
```

Or using uvicorn directly:

```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### 2. Start the Frontend Server (in a new terminal)

```bash
cd frontend
./start-frontend.sh  # On macOS/Linux
```

Or on Windows:

```bash
cd frontend
python serve.py
```

### 3. Access the Application

- **Frontend Interface**: http://localhost:3000
- Backend API: http://localhost:8000
- Interactive API Documentation: http://localhost:8000/docs
- Alternative API Documentation: http://localhost:8000/redoc

## Initial Setup

### 1. Fetch Initial News Data

Make a POST request to fetch news articles:

```bash
curl -X POST "http://localhost:8000/fetch-news" \
     -H "Content-Type: application/json" \
     -d '{"save_to_db": true}'
```

### 2. Verify Setup

Check database statistics:

```bash
curl "http://localhost:8000/stats"
```

### 3. Test Search

Search for articles:

```bash
curl -X POST "http://localhost:8000/search" \
     -H "Content-Type: application/json" \
     -d '{"query": "technology news", "max_results": 5}'
```

## Configuration Options

All configuration options can be set via environment variables in the `.env` file:

### Database Settings

- `PINECONE_API_KEY`: Your Pinecone API key
- `PINECONE_INDEX_NAME`: Name of the Pinecone index (default: "news-articles")
- `PINECONE_NAMESPACE`: Namespace within the index (default: "default")
- `RAW_NEWS_DIR`: Directory for raw news data
- `PROCESSED_NEWS_DIR`: Directory for processed news data

### API Settings

- `API_HOST`: Server host (default: 0.0.0.0)
- `API_PORT`: Server port (default: 8000)
- `API_RELOAD`: Enable auto-reload in development

### News Fetching

- `FETCH_INTERVAL_HOURS`: How often to fetch news
- `MAX_ARTICLES_PER_FEED`: Maximum articles per RSS feed

### AI Settings

- `EMBEDDING_MODEL`: Cohere embedding model
- `GROQ_MODEL`: Groq LLM model
- `MAX_TOKENS`: Maximum tokens for LLM responses
- `TEMPERATURE`: LLM temperature setting

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed and virtual environment is activated
2. **API Key Errors**: Verify API keys are correctly set in `.env` file
3. **Database Errors**: Check that data directories exist and are writable
4. **Network Errors**: Ensure internet connection for RSS feeds and API calls

### Logs

The application uses loguru for logging. Check console output for detailed error messages.

### Database Issues

If you encounter database issues, you can clear and reset:

```bash
curl -X DELETE "http://localhost:8000/clear-database"
```

## Development

### Running Tests

```bash
cd backend
pytest
```

### Code Structure

- `main.py`: FastAPI application and routes
- `config.py`: Configuration management
- `news_fetcher.py`: RSS feed fetching
- `vector_store.py`: Pinecone vector database operations
- `embeddings.py`: Cohere embedding generation
- `recommender.py`: AI-powered recommendations

### Adding New RSS Feeds

RSS feeds are organized by categories in `config.py`. To add new feeds:

1. Edit the `rss_feeds` dictionary in `config.py`
2. Add feeds to existing categories or create new categories
3. Available categories: mainstream_news, music, gaming, tech, lifestyle

Example:

```python
rss_feeds: Dict[str, List[str]] = {
    "tech": [
        "https://www.wired.com/feed/rss",
        "https://your-new-tech-feed.com/rss"  # Add new feed here
    ],
    "your_new_category": [  # Add new category
        "https://example.com/rss"
    ]
}
```
